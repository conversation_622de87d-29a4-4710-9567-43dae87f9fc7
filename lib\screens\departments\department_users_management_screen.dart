import 'package:flutter/material.dart';
import 'package:flutter_application_2/controllers/department_users_controller.dart';
import 'package:get/get.dart';

import '../../models/user_model.dart';
import '../../services/unified_permission_service.dart';


/// شاشة إدارة المستخدمين بين الأقسام
class DepartmentUsersManagementScreen extends StatelessWidget {
  const DepartmentUsersManagementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(DepartmentUsersController());
    final permissionService = Get.find<UnifiedPermissionService>();

    // 🔒 التحقق من صلاحية الوصول لهذه الشاشة
    if (!permissionService.canManageDepartmentUsers()) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('غير مسموح'),
          backgroundColor: Colors.red[700],
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.block, size: 64, color: Colors.red),
              SizedBox(height: 16),
              Text(
                'ليس لديك صلاحية لإدارة المستخدمين في الأقسام',
                style: TextStyle(fontSize: 18),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المستخدمين بين الأقسام'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.refresh(),
            tooltip: 'تحديث البيانات',
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading && controller.departments.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('جاري تحميل البيانات...'),
              ],
            ),
          );
        }

        if (controller.error.isNotEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, size: 64, color: Colors.red),
                SizedBox(height: 16),
                Text(
                  controller.error,
                  style: TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => controller.refresh(),
                  child: Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            // شريط الإحصائيات
            _buildStatisticsBar(controller),
            const Divider(height: 1),
            // المحتوى الرئيسي
            Expanded(
              child: Row(
                children: [
                  // قائمة الأقسام
                  Expanded(
                    flex: 1,
                    child: _buildDepartmentsList(controller),
                  ),
                  const VerticalDivider(width: 1),
                  // مستخدمو القسم المحدد
                  Expanded(
                    flex: 2,
                    child: _buildDepartmentUsers(controller),
                  ),
                  const VerticalDivider(width: 1),
                  // المستخدمون المتاحون
                  Expanded(
                    flex: 2,
                    child: _buildAvailableUsers(controller),
                  ),
                ],
              ),
            ),
          ],
        );
      }),
    );
  }

  /// بناء شريط الإحصائيات
  Widget _buildStatisticsBar(DepartmentUsersController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[50],
      child: Obx(() {
        final totalDepartments = controller.departments.length;
        final unassignedUsers = controller.availableUsers.length;
        final selectedDepartmentUsers = controller.departmentUsers.length;
        final selectedUsersCount = controller.selectedUserIds.length;

        return Row(
          children: [
            _buildStatCard(
              'إجمالي الأقسام',
              totalDepartments.toString(),
              Icons.business,
              Colors.blue,
            ),
            const SizedBox(width: 16),
            _buildStatCard(
              'غير محددين في أقسام',
              unassignedUsers.toString(),
              Icons.person_off,
              Colors.amber,
            ),
            const SizedBox(width: 16),
            _buildStatCard(
              'مستخدمو القسم المحدد',
              selectedDepartmentUsers.toString(),
              Icons.group,
              Colors.green,
            ),
            const SizedBox(width: 16),
            _buildStatCard(
              'المحددون للتعيين',
              selectedUsersCount.toString(),
              Icons.check_circle,
              Colors.purple,
            ),
            const Spacer(),
            // أزرار الإجراءات
            Row(
              children: [
                if (UnifiedPermissionService().canExportReport())
                  ElevatedButton.icon(
                    onPressed: () => _exportReport(controller),
                    icon: const Icon(Icons.download, size: 16),
                    label: const Text('تصدير تقرير'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.indigo[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                  ),
                const SizedBox(width: 8),
                // مؤشر حالة التحميل
                if (controller.isLoading || controller.isTransferring)
                  const Row(
                    children: [
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      SizedBox(width: 8),
                      Text('جاري المعالجة...'),
                    ],
                  ),
              ],
            ),
          ],
        );
      }),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء قائمة الأقسام
  Widget _buildDepartmentsList(DepartmentUsersController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الأقسام',
            style: Get.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.blue[700],
            ),
          ),
          const SizedBox(height: 16),
          // شريط البحث في الأقسام
          TextField(
            decoration: const InputDecoration(
              labelText: 'البحث في الأقسام',
              hintText: 'ابحث بالاسم...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
              isDense: true,
            ),
            onChanged: (value) => controller.setDepartmentSearchQuery(value),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Obx(() {
              if (controller.departments.isEmpty) {
                return const Center(
                  child: Text('لا توجد أقسام متاحة'),
                );
              }

              return ListView.builder(
                itemCount: controller.departments.length,
                itemBuilder: (context, index) {
                  final department = controller.departments[index];
                  final isSelected = controller.selectedDepartment?.id == department.id;

                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    color: isSelected ? Colors.blue[50] : null,
                    child: ListTile(
                      title: Text(
                        department.name,
                        style: TextStyle(
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          color: isSelected ? Colors.blue[700] : null,
                        ),
                      ),
                      subtitle: department.description != null
                          ? Text(department.description!)
                          : null,
                      leading: CircleAvatar(
                        backgroundColor: isSelected ? Colors.blue[700] : Colors.grey[400],
                        child: Icon(
                          Icons.business,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                      trailing: isSelected
                          ? Icon(Icons.check_circle, color: Colors.blue[700])
                          : null,
                      onTap: () => controller.selectDepartment(department),
                    ),
                  );
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة مستخدمي القسم المحدد
  Widget _buildDepartmentUsers(DepartmentUsersController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Obx(() {
            final departmentName = controller.selectedDepartment?.name ?? 'لم يتم تحديد قسم';
            return Text(
              'مستخدمو قسم: $departmentName',
              style: Get.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.green[700],
              ),
            );
          }),
          const SizedBox(height: 16),
          Expanded(
            child: Obx(() {
              if (controller.selectedDepartment == null) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.business, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'اختر قسماً لعرض مستخدميه',
                        style: TextStyle(color: Colors.grey, fontSize: 16),
                      ),
                    ],
                  ),
                );
              }

              if (controller.departmentUsers.isEmpty) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.people_outline, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'لا يوجد مستخدمون في هذا القسم',
                        style: TextStyle(color: Colors.grey, fontSize: 16),
                      ),
                    ],
                  ),
                );
              }

              return ListView.builder(
                itemCount: controller.departmentUsers.length,
                itemBuilder: (context, index) {
                  final user = controller.departmentUsers[index];
                  return _buildUserCard(user, controller, isDepartmentUser: true);
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة المستخدمين المتاحين
  Widget _buildAvailableUsers(DepartmentUsersController controller) {
    return Container(
      padding: const EdgeInsets.all(5),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'المستخدمون المتاحون',
                    style: Get.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.orange[700],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.info_outline, size: 14, color: Colors.blue[700]),
                        const SizedBox(width: 4),
                        Text(
                          'غير محددين في أي قسم',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.blue[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              ElevatedButton.icon(
                onPressed: () => controller.loadAvailableUsers(),
                icon: const Icon(Icons.refresh, size: 16),
                label: const Text('تحديث'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange[700],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // شريط البحث
          TextField(
            decoration: const InputDecoration(
              labelText: 'البحث في المستخدمين',
              hintText: 'ابحث بالاسم أو البريد الإلكتروني...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) => controller.setSearchQuery(value),
          ),
          const SizedBox(height: 8),
          // أزرار التحكم
          Obx(() {
            final hasSelection = controller.selectedUserIds.isNotEmpty;
            final canAssign = hasSelection && controller.selectedDepartment != null;

            return Row(
              children: [
                ElevatedButton.icon(
                  onPressed: controller.availableUsers.isNotEmpty
                      ? () => controller.selectAllUsers()
                      : null,
                  icon: const Icon(Icons.select_all, size: 16),
                  label: const Text('تحديد الكل'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: hasSelection ? () => controller.clearSelection() : null,
                  icon: const Icon(Icons.clear, size: 16),
                  label: const Text('إلغاء التحديد'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: canAssign && !controller.isTransferring
                      ? () => _assignSelectedUsers(controller)
                      : null,
                  icon: controller.isTransferring
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                        )
                      : const Icon(Icons.assignment_ind, size: 16),
                  label: Text('تعيين (${controller.selectedUserIds.length})'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: hasSelection && !controller.isTransferring
                      ? () => _showBulkTransferDialog(controller)
                      : null,
                  icon: const Icon(Icons.swap_horiz, size: 16),
                  label: Text('نقل مجموعي (${controller.selectedUserIds.length})'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
              ],
            );
          }),
          const SizedBox(height: 16),
          Expanded(
            child: Obx(() {
              if (controller.availableUsers.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.check_circle_outline, size: 64, color: Colors.green[400]),
                      const SizedBox(height: 16),
                      Text(
                        'ممتاز! 🎉',
                        style: TextStyle(
                          color: Colors.green[700],
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'جميع المستخدمين محددين في أقسام',
                        style: TextStyle(
                          color: Colors.green[600],
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.green.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.green.shade200),
                        ),
                        child: Text(
                          'لا يوجد مستخدمون بحاجة للتعيين',
                          style: TextStyle(
                            color: Colors.green[700],
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }

              return ListView.builder(
                itemCount: controller.availableUsers.length,
                itemBuilder: (context, index) {
                  final user = controller.availableUsers[index];
                  return _buildUserCard(user, controller, isDepartmentUser: false);
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة المستخدم
  Widget _buildUserCard(User user, DepartmentUsersController controller, {required bool isDepartmentUser}) {
    return Obx(() {
      final isSelected = controller.isUserSelected(user.id);
      
      return Card(
        margin: const EdgeInsets.only(bottom: 8),
        color: isSelected ? Colors.orange[50] : null,
        child: ListTile(
          leading: CircleAvatar(
            backgroundColor: user.isActive ? Colors.green[400] : Colors.red[400],
            child: Text(
              user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
              style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ),
          title: Text(
            user.name,
            style: TextStyle(
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(user.email ?? 'غير محدد'),
              if (user.departmentId != null)
                Text(
                  'القسم: ${controller.getDepartmentName(user.departmentId)}',
                  style: TextStyle(
                    color: Colors.blue[600],
                    fontSize: 12,
                  ),
                ),
            ],
          ),
          trailing: isDepartmentUser
              ? PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'transfer') {
                      _showTransferDialog(user, controller);
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'transfer',
                      child: Row(
                        children: [
                          Icon(Icons.swap_horiz, size: 16),
                          SizedBox(width: 8),
                          Text('نقل إلى قسم آخر'),
                        ],
                      ),
                    ),
                  ],
                )
              : Checkbox(
                  value: isSelected,
                  onChanged: (value) => controller.toggleUserSelection(user.id),
                ),
          onTap: isDepartmentUser
              ? null
              : () => controller.toggleUserSelection(user.id),
        ),
      );
    });
  }

  /// عرض حوار نقل المستخدم
  void _showTransferDialog(User user, DepartmentUsersController controller) {
    Get.dialog(
      AlertDialog(
        title: Text('نقل المستخدم: ${user.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('اختر القسم الجديد:'),
            const SizedBox(height: 16),
            DropdownButtonFormField<int>(
              decoration: const InputDecoration(
                labelText: 'القسم الجديد',
                border: OutlineInputBorder(),
              ),
              items: controller.departments
                  .where((dept) => dept.id != user.departmentId)
                  .map((dept) => DropdownMenuItem(
                        value: dept.id,
                        child: Text(dept.name),
                      ))
                  .toList(),
              onChanged: (departmentId) {
                if (departmentId != null) {
                  Get.back();
                  controller.transferUser(user.id, departmentId);
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// تعيين المستخدمين المحددين
  void _assignSelectedUsers(DepartmentUsersController controller) {
    if (controller.selectedUserIds.isEmpty) {
      Get.snackbar(
        'تنبيه',
        'يجب تحديد مستخدم واحد على الأقل',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    if (controller.selectedDepartment == null) {
      Get.snackbar(
        'تنبيه',
        'يجب تحديد قسم أولاً',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    controller.assignUsersToCurrentDepartment(controller.selectedUserIds);
  }



  /// تصدير تقرير إدارة المستخدمين
  void _exportReport(DepartmentUsersController controller) {
    try {
      final reportJson = controller.exportReportAsJson();

      // في بيئة الويب، يمكن تحميل الملف مباشرة
      // في بيئة الموبايل، يمكن حفظه في التخزين المحلي

      Get.dialog(
        AlertDialog(
          title: const Text('تصدير التقرير'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('الميزة قيد التطوير:'),

            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إغلاق'),
            ),
            ElevatedButton(
              onPressed: () {
                // نسخ التقرير إلى الحافظة
                Get.back();
                Get.snackbar(
                  'تم',
                  'تم نسخ التقرير إلى الحافظة',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              },
              child: const Text('نسخ'),
            ),
          ],
        ),
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في تصدير التقرير: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// عرض حوار النقل المجموعي
  void _showBulkTransferDialog(DepartmentUsersController controller) {
    if (controller.selectedUserIds.isEmpty) {
      Get.snackbar(
        'تنبيه',
        'يجب تحديد مستخدم واحد على الأقل',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    Get.dialog(
      AlertDialog(
        title: Text('نقل مجموعي (${controller.selectedUserIds.length} مستخدم)'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('سيتم نقل ${controller.selectedUserIds.length} مستخدم إلى القسم المحدد:'),
            const SizedBox(height: 16),
            // عرض أسماء المستخدمين المحددين
            Container(
              height: 100,
              width: double.maxFinite,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(4),
              ),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: controller.selectedUserIds.map((userId) {
                    final user = controller.availableUsers.firstWhereOrNull((u) => u.id == userId);
                    return Text('• ${user?.name ?? 'مستخدم غير معروف'} (${user?.email ?? ''})');
                  }).toList(),
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Text('اختر القسم الجديد:'),
            const SizedBox(height: 8),
            DropdownButtonFormField<int>(
              decoration: const InputDecoration(
                labelText: 'القسم الجديد',
                border: OutlineInputBorder(),
              ),
              items: controller.departments
                  .map((dept) => DropdownMenuItem(
                        value: dept.id,
                        child: Text(dept.name),
                      ))
                  .toList(),
              onChanged: (departmentId) {
                if (departmentId != null) {
                  Get.back();
                  controller.transferMultipleUsers(controller.selectedUserIds, departmentId);
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }
}
